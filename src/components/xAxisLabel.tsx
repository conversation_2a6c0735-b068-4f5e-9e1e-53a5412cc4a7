import { Label, Tag, Text } from "react-konva";
import { useEffect, useLayoutEffect, useRef, useState } from "react";
import Konva from "konva";
import { useXAxisCoordinates } from "../hooks/useCoordinates";
import { format } from "date-fns";
import { AXIS_LABEL_COLOR, } from "../commons/constants";
import { isNil } from "../commons/util";


export default function XAxisLabel(props: {
    visible?: boolean,
    time?: number,
    x?: number,
    fontColor?: string,
    backgroundColor?: string,
    borderColor?: string,
    y?: number
}) {
    // Use the new unified coordinate system
    const coords = useXAxisCoordinates();
    const labelRef = useRef<Konva.Label>(null);
    const [labelX, setLabelX] = useState(0);
    const [labelText, setLabelText] = useState('');
    const [offsetX, setOffsetX] = useState(0);

    useEffect(() => {
        if (!props.visible) return;
        
        let x: number;
        let text: string;
        
        if (!isNil(props.x)) {
            x = props.x;
            text = format(coords.xToTime(props.x), 'MM/dd HH:mm');
        } else if (!isNil(props.time)) {
            // This is the key fix - use the same coordinate system as crosshairs
            x = coords.timeToX(props.time);
            text = format(props.time, 'MM/dd HH:mm');
        } else {
            return;
        }
        
        setLabelX(x);
        setLabelText(text);
    }, [props.time, props.x, props.visible, coords]);

    // Calculate offset to center the label
    useLayoutEffect(() => {
        if (!props.visible || !labelRef.current || !labelText) return;
        
        const updateOffset = () => {
            if (labelRef.current) {
                const width = labelRef.current.getWidth();
                setOffsetX(width / 2);
            }
        };
        
        requestAnimationFrame(updateOffset);
    }, [labelText, props.visible]);

    return (
        <Label
            ref={labelRef}
            x={labelX}
            y={props.y ?? 0}
            offsetX={offsetX}
            visible={props.visible !== false}
            perfectDrawEnabled={false}
        >
            <Tag
                fill={props.backgroundColor || AXIS_LABEL_COLOR}
                stroke={props.borderColor || AXIS_LABEL_COLOR}
                strokeWidth={1}
                cornerRadius={12}
                shadowColor="rgba(0, 0, 0, 0.25)"
                shadowBlur={3}
                shadowOffsetY={1}
                shadowOffsetX={0}
            />
            <Text
                text={labelText}
                padding={8}
                fill={props.fontColor || 'white'}
                fontSize={12}
                fontStyle="bold"
            />
        </Label>
    );
}
