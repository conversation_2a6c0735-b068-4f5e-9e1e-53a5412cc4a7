import { Shape } from "react-konva";
import { type CandlestickStore } from "../../../store/candlestickStore";
import { useCoordinates } from "../../../hooks/useCoordinates";
import Konva from "konva";
import { useAppContext, useChartContext, useDataContext } from "../../../contexts/contexts";


export const CandlestickPlot = (props: { store: CandlestickStore }) => {
    console.debug('rendering candlestick plot')
    const { store } = props
    const { timeRange, timeUnit } = useAppContext()
    const { ticker } = useDataContext()
    const { valueRange, chartName } = useChartContext()
    const coords = useCoordinates(chartName)
    ticker.use()
    timeRange.use()
    valueRange.use()

    const sceneFunc = (context: Konva.Context, shape: Konva.Shape) => {
        const list = store.selectByTime(timeRange.value.min, timeRange.value.max);
        // Calculate bar width using delta time conversion
        const deltaTimeInPixels = coords.rawTimeToX(timeRange.value.min + timeUnit, timeRange.value) - coords.rawTimeToX(timeRange.value.min, timeRange.value)
        const barWidth = Math.max(1, deltaTimeInPixels - 3)
        const cornerRadius = Math.min(4, barWidth / 4);
        const minHeightForRoundCorners = cornerRadius * 2;
        const wickWidth = Math.min(barWidth, 2);

        // Set common context properties once
        context.setAttr('lineWidth', wickWidth);

        for (const candle of list) {
            const closeY = coords.valueToY(candle.close);
            if (isNaN(closeY)) continue;

            const openY = coords.valueToY(candle.open);
            const x = coords.timeToX(candle.time);
            const highY = coords.valueToY(candle.high);
            const lowY = coords.valueToY(candle.low);
            const change = candle.close - candle.open;
            const color = candle.color ?? '#26a69a';
            const isBull = change > 0;

            if (barWidth <= 2) {
                // Simple line for very thin bars
                context.setAttr('strokeStyle', color);
                context.beginPath();
                context.moveTo(x, highY);
                context.lineTo(x, lowY);
                context.stroke();
                continue;
            }

            const bodyX = x - barWidth / 2;
            const bodyY = Math.min(closeY, openY);
            const bodyHeight = Math.max(Math.abs(closeY - openY), 1);

            const gradient = context.createLinearGradient(
                bodyX,
                isBull ? bodyY : bodyY + bodyHeight,
                bodyX,
                isBull ? bodyY + bodyHeight : bodyY
            );
            gradient.addColorStop(0, color + 'FF');
            gradient.addColorStop(1, color + '80');


            // Draw wicks with gradient-based colors
            context.setAttr('strokeStyle', isBull ? color + 'FF' : color + '80');
            context.beginPath();
            context.moveTo(x, highY);
            context.lineTo(x, Math.min(closeY, openY));
            context.stroke();

            context.setAttr('strokeStyle', isBull ? color + '80' : color + 'FF');
            context.beginPath();
            context.moveTo(x, Math.max(closeY, openY));
            context.lineTo(x, lowY);
            context.stroke();

            // Draw body
            context.beginPath();
            if (bodyHeight < minHeightForRoundCorners) {
                // Simple rectangle for small bodies
                context.rect(bodyX, bodyY, barWidth, bodyHeight);
            } else {
                // Rounded rectangle for larger bodies
                context.moveTo(bodyX + cornerRadius, bodyY);
                context.lineTo(bodyX + barWidth - cornerRadius, bodyY);
                context.arcTo(bodyX + barWidth, bodyY, bodyX + barWidth, bodyY + cornerRadius, cornerRadius);
                context.lineTo(bodyX + barWidth, bodyY + bodyHeight - cornerRadius);
                context.arcTo(bodyX + barWidth, bodyY + bodyHeight, bodyX + barWidth - cornerRadius, bodyY + bodyHeight, cornerRadius);
                context.lineTo(bodyX + cornerRadius, bodyY + bodyHeight);
                context.arcTo(bodyX, bodyY + bodyHeight, bodyX, bodyY + bodyHeight - cornerRadius, cornerRadius);
                context.lineTo(bodyX, bodyY + cornerRadius);
                context.arcTo(bodyX, bodyY, bodyX + cornerRadius, bodyY, cornerRadius);
            }
            context.setAttr('fillStyle', gradient);
            context.fill();
            context.fillStrokeShape(shape);
        }

        if (store.showPriceLine && store.priceLineValue !== undefined) {
            const y = coords.valueToY(store.priceLineValue);
            context.setAttr('lineWidth', 1);
            context.setAttr('strokeStyle', store.last.color);
            context.shadowColor = 'rgba(0, 0, 0, 0.25)';
            context.shadowBlur = 5;
            context.shadowOffsetY = 2;
            context.shadowOffsetX = 0;

            context.beginPath();
            context.moveTo(0, y);
            context.lineTo(coords.timeToX(timeRange.value.max), y);
            context.stroke();

            // Reset shadow properties
            context.shadowColor = 'rgba(0, 0, 0, 0)';
            context.shadowBlur = 0;
            context.shadowOffsetY = 0;
            context.shadowOffsetX = 0;
        }
    }


    return (
        <Shape
            listening={false}
            sceneFunc={sceneFunc}
        />
    )
}
