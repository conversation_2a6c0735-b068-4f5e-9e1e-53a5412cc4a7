import { useViewport } from '../contexts/viewportContext'
import { useAppContext, useChartContext } from '../contexts/contexts'
import { TRange } from '../types/commonTypes'

export const useCoordinates = (chartName?: string) => {
  let viewport: ReturnType<typeof useViewport> | null = null

  try {
    viewport = useViewport()
  } catch (error) {
    // Fallback if ViewportProvider is not available
    console.warn('useCoordinates called outside ViewportProvider, using fallback coordinates')
    return {
      chartWidth: 900,
      chartHeight: 560,
      containerWidth: 1000,
      containerHeight: 600,
      chartArea: null,
      timeToX: () => 0,
      xToTime: () => 0,
      valueToY: () => 0,
      yToValue: () => 0,
      rawTimeToX: () => 0,
      rawXToTime: () => 0,
      rawValueToY: () => 0,
      rawYToValue: () => 0,
      deltaXToTime: () => 0,
      deltaTimeToX: () => 0,
      deltaYToValue: () => 0
    }
  }

  const { timeRange } = useAppContext()

  // Get chart-specific data if chartName is provided
  const chartContext = chartName ? useChartContext() : null
  const valueRange = chartContext?.valueRange.value || { min: 0, max: 1 }

  // Get chart-specific dimensions
  const chartArea = chartName ? viewport.chartAreas.get(chartName) : null
  const chartHeight = chartArea?.height ?? viewport.chartHeight

  return {
    // Dimensions - all components use these consistent values
    chartWidth: viewport.chartWidth,
    chartHeight: viewport.chartHeight,
    containerWidth: viewport.containerWidth,
    containerHeight: viewport.containerHeight,
    chartArea,

    // Coordinate transformations - guaranteed to be consistent across all components
    timeToX: (time: number): number => {
      return viewport.timeToX(time, timeRange.value)
    },

    xToTime: (x: number): number => {
      return viewport.xToTime(x, timeRange.value)
    },

    valueToY: (value: number): number => {
      return viewport.valueToY(value, valueRange, chartHeight)
    },

    yToValue: (y: number): number => {
      return viewport.yToValue(y, valueRange, chartHeight)
    },

    // Raw transform functions for custom ranges (advanced usage)
    rawTimeToX: (time: number, customTimeRange: TRange): number => {
      return viewport.timeToX(time, customTimeRange)
    },

    rawXToTime: (x: number, customTimeRange: TRange): number => {
      return viewport.xToTime(x, customTimeRange)
    },

    rawValueToY: (value: number, customValueRange: TRange, customHeight?: number): number => {
      return viewport.valueToY(value, customValueRange, customHeight ?? chartHeight)
    },

    rawYToValue: (y: number, customValueRange: TRange, customHeight?: number): number => {
      return viewport.yToValue(y, customValueRange, customHeight ?? chartHeight)
    },

    // Delta functions for drag operations
    deltaXToTime: (deltaX: number): number => {
      const timeSpan = timeRange.value.max - timeRange.value.min
      if (timeSpan <= 0) return 0
      return timeSpan * deltaX / viewport.chartWidth
    },

    deltaTimeToX: (deltaTime: number): number => {
      const timeSpan = timeRange.value.max - timeRange.value.min
      if (timeSpan <= 0) return 0
      return (deltaTime / timeSpan) * viewport.chartWidth
    },

    deltaYToValue: (deltaY: number): number => {
      const valueSpan = valueRange.max - valueRange.min
      if (valueSpan <= 0) return 0
      return valueSpan * deltaY / chartHeight
    }
  }
}

// Specialized hook for X-axis components (which don't need a specific chart context)
export const useXAxisCoordinates = () => {
  try {
    const viewport = useViewport()
    const { timeRange } = useAppContext()

    return {
      chartWidth: viewport.chartWidth,
      timeToX: (time: number): number => viewport.timeToX(time, timeRange.value),
      xToTime: (x: number): number => viewport.xToTime(x, timeRange.value)
    }
  } catch (error) {
    console.warn('useXAxisCoordinates called outside ViewportProvider, using fallback')
    return {
      chartWidth: 900,
      timeToX: () => 0,
      xToTime: () => 0
    }
  }
}

// Specialized hook for components that need minimal coordinate access
export const useBasicCoordinates = () => {
  try {
    const viewport = useViewport()

    return {
      chartWidth: viewport.chartWidth,
      chartHeight: viewport.chartHeight,
      chartAreas: viewport.chartAreas
    }
  } catch (error) {
    console.warn('useBasicCoordinates called outside ViewportProvider, using fallback')
    return {
      chartWidth: 900,
      chartHeight: 560,
      chartAreas: new Map()
    }
  }
}

// Specialized hook for Y-axis components (which need Y-axis specific coordinate transformations)
export const useYAxisCoordinates = () => {
  try {
    const { valueRange, yAxisWidth, height } = useChartContext()

    const valueToY = (value: number): number => {
      const valueSpan = valueRange.value.max - valueRange.value.min
      if (valueSpan <= 0) return NaN
      // Invert Y-axis (0 at top, height at bottom)
      return height - ((value - valueRange.value.min) / valueSpan * height)
    }

    const yToValue = (y: number): number => {
      const valueSpan = valueRange.value.max - valueRange.value.min
      if (valueSpan <= 0) return NaN
      // Invert Y-axis (0 at top, height at bottom)
      return valueRange.value.min + (valueSpan * (height - y) / height)
    }

    return {
      yAxisWidth,
      height,
      valueToY,
      yToValue
    }
  } catch (error) {
    console.warn('useYAxisCoordinates called outside chart context, using fallback')
    return {
      yAxisWidth: 80,
      height: 560,
      valueToY: () => 0,
      yToValue: () => 0
    }
  }
}
