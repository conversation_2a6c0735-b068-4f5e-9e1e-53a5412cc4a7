import { Shape } from "react-konva";
import { type HistogramStore } from "../../../store/histogramStore";
import { useCoordinates } from "../../../hooks/useCoordinates";
import Konva from "konva";
import { useAppContext, useChartContext, useDataContext } from "../../../contexts/contexts";
import { isNil } from "../../../commons/util";
import { useCallback } from "react";



export function HistogramPlot(props: { store: HistogramStore }) {
    console.debug('rendering histogram plot')
    const { store } = props
    const { timeRange, timeUnit } = useAppContext()
    const { valueRange, chartName } = useChartContext()
    const { ticker } = useDataContext()
    ticker.use()
    timeRange.use()
    valueRange.use()

    const coords = useCoordinates(chartName)

    const sceneFunc = useCallback((context: Konva.Context, shape: Konva.Shape) => {
        const list = store.selectByTime(timeRange.value.min, timeRange.value.max);
        // Calculate bar width using delta time conversion like candlestick plot
        const deltaTimeInPixels = coords.rawTimeToX(timeRange.value.min + timeUnit, timeRange.value) - coords.rawTimeToX(timeRange.value.min, timeRange.value)
        const barWidth = Math.max(1, deltaTimeInPixels - 3);
        const cornerRadius = Math.min(4, barWidth / 4);
        const minHeightForRoundCorners = cornerRadius * 2;
        const zeroY = coords.valueToY(0); // Baseline for histogram

        // Early exit if zeroY is invalid
        if (isNaN(zeroY)) return;


        for (const dataPoint of list) {
            if (isNil(dataPoint.value)) continue;

            const x = coords.timeToX(dataPoint.time);
            const valueY = coords.valueToY(dataPoint.value);
            const isPositive = dataPoint.value > 0;
            const color = dataPoint.color ?? '#D3ECCD'

            // Calculate bar dimensions
            const barX = x - barWidth / 2;
            const barY = isPositive ? valueY : zeroY;
            const barHeight = Math.abs(valueY - zeroY);

            if (barWidth <= 2) {
                // Simple line for very thin bars
                context.setAttr('strokeStyle', color);
                context.setAttr('lineWidth', barWidth);
                context.beginPath();
                context.moveTo(x, zeroY);
                context.lineTo(x, valueY);
                context.stroke();
                continue;
            }

            const gradient = context.createLinearGradient(
                barX,
                barY,
                barX,
                barY + barHeight
            );
            gradient.addColorStop(0, isPositive ? color + '80' : color + 'FF');
            gradient.addColorStop(1, isPositive ? color + 'FF' : color + '80');

            // Draw bar
            context.beginPath();
            if (barHeight < minHeightForRoundCorners) {
                context.rect(barX, barY, barWidth, barHeight);
            } else {
                context.moveTo(barX + cornerRadius, barY);
                context.lineTo(barX + barWidth - cornerRadius, barY);
                context.arcTo(barX + barWidth, barY, barX + barWidth, barY + cornerRadius, cornerRadius);
                context.lineTo(barX + barWidth, barY + barHeight - cornerRadius);
                context.arcTo(barX + barWidth, barY + barHeight, barX + barWidth - cornerRadius, barY + barHeight, cornerRadius);
                context.lineTo(barX + cornerRadius, barY + barHeight);
                context.arcTo(barX, barY + barHeight, barX, barY + barHeight - cornerRadius, cornerRadius);
                context.lineTo(barX, barY + cornerRadius);
                context.arcTo(barX, barY, barX + cornerRadius, barY, cornerRadius);
            }
            context.setAttr('fillStyle', gradient);
            context.fill();
            context.fillStrokeShape(shape);
        }
    }, [store, timeRange, valueRange, timeUnit, coords])

    return (
        <Shape
            listening={false}
            sceneFunc={sceneFunc}
            perfectDrawEnabled={false}
        />
    )
}
