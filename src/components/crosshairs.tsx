import { Shape } from "react-konva";
import Kon<PERSON> from "konva";
import { useAppContext, useChartContext } from "../contexts/contexts";
import { useCoordinates } from "../hooks/useCoordinates";
import { CROSSHAIR_COLOR } from "../commons/constants";

export const Crosshairs = () => {
    const { activeChart, crosshairsValue } = useAppContext();
    const { chartStore } = useChartContext();
    const coords = useCoordinates(chartStore.name);
    crosshairsValue.use();

    const sceneFunc = (context: Konva.Context) => {
        if (!crosshairsValue.value || crosshairsValue.value.value == null) return;
        
        // Use the same coordinate system as X-axis label
        const x = coords.timeToX(crosshairsValue.value.time);
        const y = coords.valueToY(crosshairsValue.value.value!);

        context.save();
        context.setLineDash([5, 5]);
        context.strokeStyle = CROSSHAIR_COLOR;
        context.lineWidth = 1.5;

        // horizontal (only on active chart)
        if (activeChart.value === chartStore.name && activeChart.value !== '') {
            context.beginPath();
            context.moveTo(0, y);
            context.lineTo(coords.chartWidth, y);
            context.stroke();
        }

        // vertical (always) - this will now align perfectly with X-axis label
        context.beginPath();
        context.moveTo(x, 0);
        context.lineTo(x, coords.chartHeight);
        context.stroke();

        context.restore();
    };


    return crosshairsValue.value ? (
        <Shape sceneFunc={sceneFunc} listening={false} perfectDrawEnabled={false} />
    ) : null;
};
