import { Layer, Stage } from 'react-konva'
import { XAXIS_HEIGHT } from '../commons/constants'
import { useBasicCoordinates } from '../hooks/useCoordinates'
import Konva from 'konva'
import { useAppContext } from '../contexts/contexts'
import XAxisLabel from './xAxisLabel'

export const XAxis = () => {
    console.debug('rendering x axis')
    const coords = useBasicCoordinates()
    const { crosshairsValue, activeChart } = useAppContext()
    crosshairsValue.use()
    return (
        <Stage
            width={coords.chartWidth}
            height={XAXIS_HEIGHT}
            onMouseEnter={onMouseEnter}
        >
            <Layer>
                <XAxisLabel
                    visible={!!crosshairsValue.value}
                    time={crosshairsValue.value?.time}
                    y={1}
                />
            </Layer>
        </Stage>
    )


    function onMouseEnter(e: Konva.KonvaEventObject<MouseEvent>) {
        crosshairsValue.value = null
        activeChart.value = ''
    }
}
