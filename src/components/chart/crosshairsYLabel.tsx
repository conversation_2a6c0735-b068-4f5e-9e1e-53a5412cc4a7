import { AXIS_LABEL_COLOR } from "../../commons/constants"
import { useAppContext, useChartContext } from "../../contexts/contexts"
import { useCoordinates } from "../../hooks/useCoordinates"
import { YAxisLabel } from "./yAxisLabel"

export const CrosshairsYLabel = () => {
    const { crosshairsValue, activeChart } = useAppContext()
    const { chartName } = useChartContext()
    const coords = useCoordinates(chartName)
    crosshairsValue.use()

    const y = crosshairsValue.value && crosshairsValue.value.value != null ? coords.valueToY(crosshairsValue.value.value) : undefined
    return <YAxisLabel visible={!!crosshairsValue.value && activeChart.value === chartName} y={y} fontColor="white" backgroundColor={AXIS_LABEL_COLOR} borderColor={AXIS_LABEL_COLOR} />
}