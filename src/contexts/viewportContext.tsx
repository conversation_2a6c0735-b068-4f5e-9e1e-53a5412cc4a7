import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react'
import { XAXIS_HEIGHT, YAXIS_WIDTH } from '../commons/constants'
import { TViewportContext, TViewport } from '../types/contextTypes'
import { TRange } from '../types/commonTypes'
import { ChartStore } from '../store/chartStore'

const ViewportContext = createContext<TViewportContext | null>(null)

export const ViewportProvider = ({
  children,
  container,
  chartStoreMap
}: {
  children: ReactNode
  container: React.RefObject<HTMLDivElement | null>
  chartStoreMap: Map<string, ChartStore> | null
}) => {
  const [viewport, setViewport] = useState<TViewport | null>(null)

  const calculateLayout = useCallback(() => {
    if (!container.current || !chartStoreMap) return

    const containerWidth = container.current.clientWidth
    const containerHeight = container.current.clientHeight
    const chartWidth = containerWidth - YAXIS_WIDTH
    const chartHeight = containerHeight - XAXIS_HEIGHT

    // Calculate chart areas using the same logic as the old LayoutContext
    const chartCount = chartStoreMap.size
    const totalFlex = 4 + (chartCount - 1)
    const chartAreas = new Map<string, { height: number, top: number }>()

    const storeList = Array.from(chartStoreMap.values())
    for (let i = 0; i < chartCount; i++) {
      const flex = i === 0 ? 4 : 1
      const height = (flex / totalFlex) * chartHeight
      const top = i === 0 ? 0 : ((4 + i - 1) / totalFlex) * chartHeight
      chartAreas.set(storeList[i].name, { height, top })
    }

    setViewport({
      containerWidth,
      containerHeight,
      chartWidth,
      chartHeight,
      chartAreas
    })
  }, [container, chartStoreMap])

  // Effect to trigger initial calculation when chartStoreMap becomes available
  useEffect(() => {
    if (container.current && chartStoreMap && chartStoreMap.size > 0) {
      calculateLayout()
    }
  }, [chartStoreMap, calculateLayout])

  useEffect(() => {
    if (!container.current) return

    const resizeObserver = new ResizeObserver(calculateLayout)
    resizeObserver.observe(container.current)
    
    // Add listeners for monitor changes and window movement (same as old LayoutContext)
    const handleWindowResize = () => calculateLayout()
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        calculateLayout()
      }
    }

    const mediaQuery = window.matchMedia(`(resolution: ${window.devicePixelRatio}dppx)`)
    const handleDPIChange = () => calculateLayout()

    window.addEventListener('resize', handleWindowResize)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    mediaQuery.addEventListener('change', handleDPIChange)
    
    calculateLayout() // Initial calculation

    return () => {
      resizeObserver.disconnect()
      window.removeEventListener('resize', handleWindowResize)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      mediaQuery.removeEventListener('change', handleDPIChange)
    }
  }, [calculateLayout])

  // Unified coordinate transformation functions
  const timeToX = useCallback((time: number, timeRange: TRange): number => {
    if (!viewport) return 0
    const timeSpan = timeRange.max - timeRange.min
    if (timeSpan <= 0) return 0
    return ((time - timeRange.min) / timeSpan) * viewport.chartWidth
  }, [viewport])

  const xToTime = useCallback((x: number, timeRange: TRange): number => {
    if (!viewport) return 0
    const timeSpan = timeRange.max - timeRange.min
    if (timeSpan <= 0) return 0
    return timeRange.min + (timeSpan * x / viewport.chartWidth)
  }, [viewport])

  const valueToY = useCallback((value: number, valueRange: TRange, chartHeight: number): number => {
    const valueSpan = valueRange.max - valueRange.min
    if (valueSpan <= 0) return 0
    // Invert Y-axis (0 at top, height at bottom)
    return chartHeight - ((value - valueRange.min) / valueSpan * chartHeight)
  }, [])

  const yToValue = useCallback((y: number, valueRange: TRange, chartHeight: number): number => {
    const valueSpan = valueRange.max - valueRange.min
    if (valueSpan <= 0) return 0
    // Invert Y-axis (0 at top, height at bottom)
    return valueRange.min + (valueSpan * (chartHeight - y) / chartHeight)
  }, [])

  if (!viewport) {
    // Provide fallback viewport during initialization to prevent hook errors
    const fallbackValue: TViewportContext = {
      containerWidth: 1000,
      containerHeight: 600,
      chartWidth: 900,
      chartHeight: 560,
      chartAreas: new Map(),
      timeToX: () => 0,
      xToTime: () => 0,
      valueToY: () => 0,
      yToValue: () => 0
    }
    return (
      <ViewportContext.Provider value={fallbackValue}>
        {children}
      </ViewportContext.Provider>
    )
  }

  const value: TViewportContext = {
    ...viewport,
    timeToX,
    xToTime,
    valueToY,
    yToValue
  }

  return (
    <ViewportContext.Provider value={value}>
      {children}
    </ViewportContext.Provider>
  )
}

export const useViewport = (): TViewportContext => {
  const context = useContext(ViewportContext)
  if (!context) {
    throw new Error('useViewport must be used within ViewportProvider')
  }
  return context
}
