import { useState, useCallback, useRef, useEffect } from "react";
import Kon<PERSON> from "konva";
import { ETool, TDrawingConfig } from "../types/toolboxTypes";
import { useAppContext, useDataContext } from "../contexts/contexts";
import { createDrawingConfig, deleteDrawingConfig, fetchDrawingConfigs, updateDrawingConfig } from "../commons/api";
import { TCoord } from "../types/commonTypes";
import { TDataPoint } from "../types/plotTypes";
import { useDrawingScale } from "./useScale";
import { useCoordinates } from "./useCoordinates";
import { DEFAULT_DRAWING_COLOR, ALERT_COLOR } from "../commons/constants";
import { useChartContext } from "../contexts/contexts";

type UseDrawingProps = {
    groupRef: React.RefObject<Konva.Group | null>;
    config: TDrawingConfig;
    anchorCount?: number;
    onDrawEnd?: () => void | Promise<void>;
    onDragEnd?: () => void | Promise<void>;
}

export function useDrawing({
    groupRef,
    config,
    anchorCount = 2,
}: UseDrawingProps) {
    const [dataPoints, setDataPoints] = useState<TDataPoint[]>(config.dataPoints || []);
    const [isSelected, setIsSelected] = useState(false);
    const [isDragging, setIsDragging] = useState(false);
    const { selectedDrawingConfig, crosshairsValue } = useAppContext();
    const { alertConfigList } = useDataContext();
    const { chartName, valueRange } = useChartContext();
    selectedDrawingConfig.use()
    alertConfigList.use()
    valueRange.use() // Ensure reactivity to Y-axis zoom changes
    const dragStartCrosshairsValue = useRef<TDataPoint | null>(null);
    const dragStartGroupCoord = useRef<TCoord | null>(null);
    const coords = useCoordinates(chartName);
    const { toRelativeCoord, toAbsoluteCoord } = useDrawingScale(groupRef)

    let anchorCoords = []
    for (let i = 0; i < anchorCount; i++) {
        const coord = toRelativeCoord({ x: coords.timeToX(dataPoints[i].time), y: coords.valueToY(dataPoints[i].value!) })
        if (isNaN(coord.y)) {
            anchorCoords = []
            break
        }
        anchorCoords.push(coord)
    }

    const toggleAnchors = (show: boolean) => {
        groupRef.current?.find(".anchor").forEach((anchor) => (show ? anchor.show() : anchor.hide()));
    };

    const setCursor = (style: "pointer" | "default") => {
        if (!groupRef.current || !groupRef.current.getStage()) return;
        groupRef.current.getStage()!.container().style.cursor = style;
    };

    const moveToTop = () => {
        if (!groupRef.current) return;
        groupRef.current.moveToTop();
    };

    const select = () => {
        selectedDrawingConfig.value = config;
        setIsSelected(true);
        toggleAnchors(true);
        moveToTop();
    }

    const deselect = () => {
        selectedDrawingConfig.value = null;
        setIsSelected(false);
        toggleAnchors(false);
        setCursor("default");
    };

    const addAnchor = () => {
        if (!crosshairsValue.value || crosshairsValue.value.value == null || dataPoints.length >= anchorCount) return;
        setDataPoints((prev) => [
            ...prev,
            { time: crosshairsValue.value!.time, value: crosshairsValue.value!.value! },
        ]);
    }


    useEffect(() => {
        const stage = groupRef.current?.getStage();
        if (!stage) return;

        const emptyClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
            if (e.target === stage && e.evt.button === 0) deselect();
        };
        stage.on("click", emptyClick);
        return () => {
            stage.off("click", emptyClick);
        };
    }, [groupRef, deselect]);

    useEffect(() => {
        toggleAnchors(false);
    }, []);

    useEffect(() => {
        const onKeyDown = async (e: KeyboardEvent) => {
            if (e.key !== "Backspace" || !isSelected || !config.id) return;
            e.stopPropagation();
            await deleteDrawingConfig(config.id);
            deselect();
            groupRef.current?.destroy();
        };

        document.addEventListener("keydown", onKeyDown);
        return () => document.removeEventListener("keydown", onKeyDown);
    }, [isSelected, config.id, deselect]);

    useEffect(() => {
        if (selectedDrawingConfig.value?.id !== config.id) {
            setIsSelected(false);
            toggleAnchors(false);
        }
    }, [selectedDrawingConfig.value, config.id]);




    const handleDragMove = useCallback(
        (_e: Konva.KonvaEventObject<DragEvent>) => {
            const anchors = groupRef.current?.find(".anchor");
            if (!anchors) return;
            const newDataPoints = anchors.map((dot) => {
                const coord = toAbsoluteCoord(dot.position())
                return { time: coords.xToTime(coord.x), value: coords.yToValue(coord.y) }
            });
            setDataPoints(newDataPoints);
        },
        [coords.xToTime, coords.yToValue, toAbsoluteCoord]
    );

    const handleDragStart = () => {
        dragStartCrosshairsValue.current = crosshairsValue.value;
        dragStartGroupCoord.current = groupRef.current?.position() || null;
        setCursor("pointer");
        setIsDragging(true);
    }

    const handleDragEnd = () => {
        dragStartCrosshairsValue.current = null;
        dragStartGroupCoord.current = null;
        if (config.id) {
            updateDrawingConfig(config.id, dataPoints);
        }
        setCursor("default");
        setIsDragging(false);
    }

    const handleClick = () => {
        if (config.id) select();
    };

    const handleMouseOver = () => {
        toggleAnchors(true);
        setCursor("pointer");
    }

    const handleMouseOut = () => {
        if (isDragging || isSelected) return;
        toggleAnchors(false);
        setCursor("default");
    }

    const groupDragBoundFunc = (pos: TCoord) => {
        if (!crosshairsValue.value || crosshairsValue.value.value == null || !dragStartCrosshairsValue.current || !dragStartGroupCoord.current) return pos;
        const deltaT = dragStartCrosshairsValue.current.time - crosshairsValue.value.time;
        const deltaX = coords.deltaTimeToX(deltaT);
        return { y: pos.y, x: dragStartGroupCoord.current.x - deltaX };
    }

    const anchorDragBoundFunc = (pos: TCoord) => ({
        y: pos.y,
        x: crosshairsValue.value ? coords.timeToX(crosshairsValue.value.time) : pos.x,
    })

    const anchorSnapTo = (index: number, dataPoint: TDataPoint) => {
        setDataPoints((prev) => {
            const newDataPoints = [...prev];
            newDataPoints[index] = dataPoint;
            return newDataPoints;
        });
    }
    const hasAlert = alertConfigList.value.some(alert => alert.arg2?.type === 'drawing' && alert.arg2.refId === config.id?.toString());
    const drawingColor = hasAlert ? ALERT_COLOR : DEFAULT_DRAWING_COLOR;
    const alertConfig = alertConfigList.value.find(alert => alert.arg2?.type === 'drawing' && alert.arg2.refId === config.id?.toString());
    const alertId = alertConfig?.id ? alertConfig.id.toString().substring(0, 6) : null;

    return {
        dataPoints,
        anchorCoords,
        addAnchor,
        drawingColor,
        lineDash: [],
        handleDragMove,
        handleDragStart,
        handleDragEnd,
        handleClick,
        handleMouseOver,
        handleMouseOut,
        groupDragBoundFunc,
        anchorDragBoundFunc,
        anchorSnapTo,
        alertId,
        hasAlert
    };
}


export const usePreviewDrawing = (props: {
    groupRef: React.RefObject<Konva.Group | null>,
    type: ETool,
    anchorCount: number,
    onDrawEnd?: () => void | Promise<void>;
}) => {
    const { groupRef, anchorCount } = props
    const [dataPoints, setDataPoints] = useState<TDataPoint[]>([]);
    const { crosshairsValue, activeTool, symbol, timeframe } = useAppContext();
    const { chartName, chartDrawingConfigList, valueRange } = useChartContext();
    const coords = useCoordinates(chartName);
    const { toRelativeCoord } = useDrawingScale(groupRef)
    crosshairsValue.use()
    valueRange.use() // Ensure reactivity to Y-axis zoom changes


    const previewCoord = (crosshairsValue.value && crosshairsValue.value.value != null)
        ? toRelativeCoord({ x: coords.timeToX(crosshairsValue.value.time), y: coords.valueToY(crosshairsValue.value.value!) })
        : null;
    const anchorCoords = dataPoints.map((dp) => toRelativeCoord({ x: coords.timeToX(dp.time), y: coords.valueToY(dp.value!) }))

    const addAnchor = () => {
        if (!crosshairsValue.value || crosshairsValue.value.value == null || dataPoints.length >= anchorCount) return;
        setDataPoints((prev) => [
            ...prev,
            { time: crosshairsValue.value!.time, value: crosshairsValue.value!.value! },
        ]);
    }

    useEffect(() => {
        if (!groupRef.current) return
        const stage = groupRef.current.getStage()!
        if (dataPoints.length === anchorCount) return
        stage.on('click', addAnchor)
        return () => {
            stage.off('click', addAnchor)
        }

    }, [groupRef])

    // handle draw end
    useEffect(() => {
        if (!groupRef.current) return
        if (dataPoints.length === anchorCount) {
            createDrawingConfig({
                timeframe: timeframe,
                symbol: symbol,
                chart: chartName,
                type: props.type,
                dataPoints,
            }).finally(async () => {
                groupRef.current?.getStage()?.off("click", addAnchor);
                await props.onDrawEnd?.();
                fetchDrawingConfigs({ symbol, chart: chartName }).then(data => {
                    chartDrawingConfigList.value = data
                }).finally(() => {
                    activeTool.value = ETool.None;
                })
            });
        }
    }, [groupRef, dataPoints])



    return {
        previewCoord,
        anchorCoords,
        addAnchor,
        drawingColor: DEFAULT_DRAWING_COLOR,
        lineDash: []
    };
}