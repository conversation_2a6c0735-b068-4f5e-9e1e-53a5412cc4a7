import { ReactNode, useMemo } from "react";
import { timeframe2timeUnit } from "../commons/util";
import { useSignal } from "../hooks/useSignal";
import { TRange } from "../types/commonTypes";
import { TDataPoint } from "../types/plotTypes";
import { TDrawingConfig } from "../types/toolboxTypes";
import { AppContext } from "./contexts";
import { TAppContext } from "../types/contextTypes";
import { TContextMenuState } from "../types/contextMenuTypes";

export const AppContextProvider = (props: { children: ReactNode, symbol: string, timeframe: string, defaultTimeRange?: TRange }) => {
    console.debug('rendering app context provider')
    const timeUnit = timeframe2timeUnit(props.timeframe);
    const activeChart = useSignal('');
    const activeTool = useSignal('');
    const selectedDrawingConfig = useSignal<TDrawingConfig | null>(null);
    const crosshairsValue = useSignal<TDataPoint | null>(null);
    const contextMenuState = useSignal<TContextMenuState | null>(null);

    const timeRange = useSignal<TRange>(props.defaultTimeRange ?? {
        max: Date.now(),
        min: Date.now() - timeUnit * 100
    });



    const value: TAppContext = useMemo(() => ({
        selectedDrawingConfig,
        timeUnit,
        symbol: props.symbol,
        timeframe: props.timeframe,
        activeChart,
        crosshairsValue,
        activeTool,
        timeRange,
        contextMenuState
    }), [props.symbol, props.timeframe, timeUnit, activeChart, activeTool, crosshairsValue, timeRange, contextMenuState]);

    return (
        <AppContext.Provider value={value}>
            {props.children}
        </AppContext.Provider>
    );
}