import { AXIS_LABEL_COLOR } from "../../commons/constants"
import { useAppContext, useChartContext } from "../../contexts/contexts"
import { usePaneScale } from "../../hooks/useScale"
import { YAxisLabel } from "./yAxisLabel"

export const CrosshairsYLabel = () => {
    const { crosshairsValue, activeChart } = useAppContext()
    const { chartName } = useChartContext()
    const { v2y } = usePaneScale()
    crosshairsValue.use()

    const y = crosshairsValue.value ? v2y(crosshairsValue.value.value) : undefined
    return <YAxisLabel visible={!!crosshairsValue.value && activeChart.value === chartName} y={y} fontColor="white" backgroundColor={AXIS_LABEL_COLOR} borderColor={AXIS_LABEL_COLOR} />
}