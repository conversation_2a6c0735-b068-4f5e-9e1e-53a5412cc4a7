import { useCallback } from 'react';
import { Shape } from 'react-konva';
import Konva from 'konva';
import { useAppContext, useChartContext, useDataContext } from '../../contexts/contexts';
import { useCoordinates } from '../../hooks/useCoordinates';
import { TOrderData } from '../../types/tradingTypes';
import { CandlestickStore } from '../../store/candlestickStore';
import { FONT_FAMILY } from '../../commons/constants';
import { findClosestDivisible } from '../../commons/util';

export const OrderMarkers = () => {
    console.debug('rendering OrderMarkers');

    const { timeRange, timeUnit } = useAppContext();
    const { chartStore, chartName } = useChartContext();
    const { ticker, orders } = useDataContext();
    const coords = useCoordinates(chartName);

    // Use reactive signals
    timeRange.use();
    ticker.use();
    orders.use();

    // Get the main candlestick store
    const mainPlotStore = chartStore.mainPlotStore as CandlestickStore;

    const sceneFunc = useCallback((context: Konva.Context) => {
        if (!orders.value.length || !mainPlotStore || chartName !== 'main') {
            return;
        }

        // Filter orders within the visible time range
        const visibleOrders = orders.value.filter(order => {
            const orderTime = parseInt(order.createdAt);
            return orderTime >= timeRange.value.min && orderTime <= timeRange.value.max;
        });

        if (!visibleOrders.length) {
            return;
        }

        // Get candlestick data for the visible time range
        const candlesticks = mainPlotStore.selectByTime(timeRange.value.min, timeRange.value.max);

        // Group orders by candlestick timespan
        const orderGroups = new Map<number, TOrderData[]>();

        visibleOrders.forEach(order => {
            const orderTime = parseInt(order.createdAt);

            // Find the closest candlestick time using findClosestDivisible
            const candleTime = findClosestDivisible(orderTime, timeUnit, 'lower');

            // Check if this candlestick exists in our data
            const candlestick = candlesticks.find(c => c.time === candleTime);

            if (candlestick) {
                if (!orderGroups.has(candleTime)) {
                    orderGroups.set(candleTime, []);
                }
                orderGroups.get(candleTime)!.push(order);
            }
        });

        // Drawing constants
        const radius = 8;
        const fontSize = 11;
        const buyColor = '#26a69a';
        const sellColor = '#ef5350';

        // Set common context properties
        context.setAttr('font', `bold ${fontSize}px ${FONT_FAMILY}`);
        context.setAttr('textAlign', 'center');
        context.setAttr('textBaseline', 'middle');

        // Draw all order markers
        orderGroups.forEach((groupOrders, candleTime) => {
            // Sort orders by creation time
            groupOrders.sort((a, b) => parseInt(a.createdAt) - parseInt(b.createdAt));

            const candlestick = candlesticks.find(c => c.time === candleTime);
            if (!candlestick) return;

            const x = coords.timeToX(candleTime);
            const candleHigh = coords.valueToY(candlestick.high);
            const candleLow = coords.valueToY(candlestick.low);

            // Separate buy and sell orders
            const buyOrders = groupOrders.filter(order => order.side === 'buy');
            const sellOrders = groupOrders.filter(order => order.side === 'sell');

            // Draw buy orders below the candlestick
            buyOrders.forEach((order, index) => {
                const stackOffset = (index + 1) * 25;
                const y = candleLow + stackOffset;

                drawOrderMarker(context, x, y, buyColor, order.isClose ? 'C' : 'B', radius);
            });

            // Draw sell orders above the candlestick
            sellOrders.forEach((order, index) => {
                const stackOffset = (index + 1) * 25;
                const y = candleHigh - stackOffset;

                drawOrderMarker(context, x, y, sellColor, order.isClose ? 'C' : 'S', radius);
            });
        });
    }, [orders.value, mainPlotStore, timeRange.value, timeUnit, chartName, coords.timeToX, coords.valueToY]);

    return (
        <Shape
            sceneFunc={sceneFunc}
            listening={false}
            perfectDrawEnabled={false}
        />
    );
};

// Helper function to draw a single order marker
function drawOrderMarker(
    context: Konva.Context,
    x: number,
    y: number,
    color: string,
    text: string,
    radius: number
) {
    // Draw circle with shadow
    context.save();
    context.shadowColor = 'rgba(0,0,0,0.3)';
    context.shadowBlur = 4;
    context.shadowOffsetX = 1;
    context.shadowOffsetY = 1;

    // Draw filled circle
    context.beginPath();
    context.arc(x, y, radius, 0, 2 * Math.PI);
    context.setAttr('fillStyle', color);
    context.fill();

    // // Draw white border
    // context.setAttr('strokeStyle', 'white');
    // context.setAttr('lineWidth', 2);
    // context.stroke();

    context.restore();

    // Draw text
    context.setAttr('fillStyle', 'white');
    context.fillText(text, x, y);
}




