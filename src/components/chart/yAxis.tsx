import { Layer, Stage } from 'react-konva'
import Konva from 'konva';
import { useAppContext, useChartContext, useDataContext } from '../../contexts/contexts';
import { YAxisContainer } from '../../styles/chartStyles';
import { YAxisLabel } from './yAxisLabel';
import { CrosshairsYLabel } from './crosshairsYLabel';
import { isNumber } from '../../commons/util';

export const YAxis = () => {
    console.debug('rendering y axis')
    const { valueRange, autoScale, yAxisWidth, height, chartStore } = useChartContext()
    const { activeChart, crosshairsValue } = useAppContext()
    const { ticker } = useDataContext()
    ticker.use()
    valueRange.use()

    return (
        <YAxisContainer>
            <Stage
                width={yAxisWidth}
                height={height}
                onWheel={onWheel}
                onDblClick={onDblClick}
                onMouseEnter={onMouseEnter}
            >
                <Layer>
                    {
                        chartStore &&
                        chartStore.plotStoreList.map(plotStore => {
                            const priceLineValue = plotStore.priceLineValue
                            if (plotStore.showPriceLine && isNumber(priceLineValue)) {
                                return <YAxisLabel
                                    key={`label-${plotStore.id}`}
                                    value={plotStore.priceLineValue!}
                                    backgroundColor="white"
                                    fontColor={plotStore.last.color}
                                    borderColor={plotStore.last.color}
                                />
                            }
                            return null
                        })
                    }
                </Layer>
                <Layer>
                    <CrosshairsYLabel />
                </Layer>
            </Stage>
        </YAxisContainer>

    )

    function onMouseEnter(e: Konva.KonvaEventObject<MouseEvent>) {
        crosshairsValue.value = null
        activeChart.value = ''
    }

    function onWheel(e: Konva.KonvaEventObject<WheelEvent>) {
        e.evt.preventDefault()
        const wheelDelta = e.evt.deltaY
        const zoomFactor = Math.pow(0.999, -wheelDelta);

        const prev = valueRange.value
        if (!prev) return
        const span = prev.max - prev.min
        const newSpan = span * zoomFactor
        const centerTime = prev.min + (span / 2)
        const newMin = centerTime - (newSpan / 2)
        const newMax = centerTime + (newSpan / 2)
        valueRange.value = {
            min: newMin,
            max: newMax
        }
        autoScale.value = false
    }

    function onDblClick(_e: Konva.KonvaEventObject<MouseEvent>) {
        autoScale.value = true
    }
}
